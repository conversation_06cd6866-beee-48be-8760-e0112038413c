<?php

/**
 * Sets up theme defaults and registers support for various WordPress features.
 */
function survilla_setup() {

	// Support for various features
	add_theme_support('automatic-feed-links');
	add_theme_support('title-tag');
	add_theme_support('html5', ['search-form', 'comment-form', 'comment-list', 'gallery', 'caption', 'style', 'script']);

	// Register navigation menus
	register_nav_menus(
		[
			'menu-1' => esc_html__('Hlavička', 'survilla'),
			'menu-2' => esc_html__('Patička', 'survilla'),
			'menu-3' => esc_html__('Copyright', 'survilla'),
		]	
	);

}
add_action('after_setup_theme', 'survilla_setup');

/**
 * Enqueue scripts and styles.
 */
function survilla_scripts() {

	// Theme
	wp_enqueue_style('survilla-style', get_template_directory_uri() . '/assets/css/main.css', [], file_exists(get_template_directory() . '/assets/css/main.css') ? filemtime(get_template_directory() . '/assets/css/main.css') : null);
	wp_enqueue_script('survilla-script', get_template_directory_uri() . '/assets/js/main.js', ['swiper-script'], file_exists(get_template_directory() . '/assets/js/main.js') ? filemtime(get_template_directory() . '/assets/js/main.js') : null, true);
	wp_localize_script('survilla-script', 'survilla_vars', [
		'calendly_id' => get_field('calendly_id', 'option'),
		'calendly_url' => get_field('calendly_url', 'option')
	]);

	// Swiper slider
	wp_enqueue_style('swiper-style', get_template_directory_uri() . '/assets/swiper/swiper-bundle.min.css', [], file_exists(get_template_directory() . '/assets/swiper/swiper-bundle.min.css') ? filemtime(get_template_directory() . '/assets/swiper/swiper-bundle.min.css') : null);
	wp_enqueue_script('swiper-script', get_template_directory_uri() . '/assets/swiper/swiper-bundle.min.js', [], file_exists(get_template_directory() . '/assets/swiper/swiper-bundle.min.js') ? filemtime(get_template_directory() . '/assets/swiper/swiper-bundle.min.js') : null, true);

	// Fontawesome
	wp_enqueue_style('fontawesome', get_template_directory_uri() . '/fonts/fontawesome/css/all.min.css', [], file_exists(get_template_directory() . '/fonts/fontawesome/css/all.min.css') ? filemtime(get_template_directory() . '/fonts/fontawesome/css/all.min.css') : null);

	// Calendly
	wp_enqueue_style('calendly-style', 'https://assets.calendly.com/assets/external/widget.css', [], null);
	wp_enqueue_script('calendly-script', 'https://assets.calendly.com/assets/external/widget.js', [], null, true);

}
add_action('wp_enqueue_scripts', 'survilla_scripts');

/**
 * Enqueue scripts and styles in admin.
 */
function survilla_admin_scripts() {

	// Fontawesome
	wp_enqueue_style(
		'fontawesome-core', 
		get_template_directory_uri() . '/fonts/fontawesome/css/fontawesome.min.css',
		[], 
		file_exists(get_template_directory() . '/fonts/fontawesome/css/fontawesome.min.css') ? filemtime(get_template_directory() . '/fonts/fontawesome/css/fontawesome.min.css') : null
	);
	wp_enqueue_style(
		'fontawesome-solid', 
		get_template_directory_uri() . '/fonts/fontawesome/css/solid.min.css',
		['fontawesome-core'], 
		file_exists(get_template_directory() . '/fonts/fontawesome/css/solid.min.css') ? filemtime(get_template_directory() . '/fonts/fontawesome/css/solid.min.css') : null
	);

}
add_action('admin_enqueue_scripts', 'survilla_admin_scripts');

/**
 * Allow SVG files.
 */
function survilla_mime_types($mimes) {
	$mimes['svg'] = 'image/svg+xml';
	return $mimes;
}
add_filter('upload_mimes', 'survilla_mime_types');

/**
 * Allow SVG in media library.
 */
function survilla_svg_check($checked, $file, $filename, $mimes, $real_mime) {
	if ($real_mime === 'image/svg+xml') {
		$checked['ext']  = 'svg';
		$checked['type'] = 'image/svg+xml';
	}
	return $checked;
}
add_filter('wp_check_filetype_and_ext', 'survilla_svg_check', 10, 5);

/**
 * Allow SVG in post content.
 */
function survilla_svg_allowed_html($allowed, $context) {
	if ($context === 'post') {
		$allowed['svg'] = [
			'xmlns' => true,
			'viewBox' => true,
			'width' => true,
			'height' => true,
			'fill' => true,
			'class' => true,
			'id' => true,
			'title' => true,
			'g' => true,
			'symbol' => true,
			'use' => true,
			'path' => [
				'd' => true,
				'fill-rule' => true,
				'clip-rule' => true,
				'class' => true
			],
			'span' => [
				'class' => true
			]
		];
	}
	return $allowed;
}
add_filter('wp_kses_allowed_html', 'survilla_svg_allowed_html', 10, 2);

/**
 * Add year shortcode.
 */
function survilla_year_shortcode() {
	$year = date_i18n('Y');
	return $year;
}
add_shortcode('year', 'survilla_year_shortcode');

/**
 * Add Cloudflare Turnstile to contact form.
 */
function survilla_add_turnstile_to_form($form) {
	$form = str_replace('<div class="col turnstile__col">', '<div class="col turnstile__col">' . do_shortcode('[cf_turnstile]'), $form);
	return $form;
}
add_filter('hf_form_markup', 'survilla_add_turnstile_to_form');

/**
 * Validate form.
 */
function survilla_validate_form($error_code, $form, $data) {
	// if ( $form->ID != 704 ) {
	// 	return $error_code;
	// }

	// $error_code = 'wrong_answer';

	return $error_code;
}
add_filter('hf_validate_form', 'survilla_validate_form', 10, 3);
