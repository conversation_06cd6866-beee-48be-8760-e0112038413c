/*!
 * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license (Commercial License)
 * Copyright 2024 Fonticons, Inc.
 */
:root, :host {
  --fa-style-family-duotone: 'Font Awesome 6 Duotone';
  --fa-font-duotone-thin: normal 100 1em/1 'Font Awesome 6 Duotone'; }

@font-face {
  font-family: 'Font Awesome 6 Duotone';
  font-style: normal;
  font-weight: 100;
  font-display: block;
  src: url("../webfonts/fa-duotone-thin-100.woff2") format("woff2"), url("../webfonts/fa-duotone-thin-100.ttf") format("truetype"); }

.fadt,
.fa-duotone.fa-thin {
  position: relative;
  font-weight: 100;
  letter-spacing: normal; }

.fadt::before,
.fa-duotone.fa-thin::before {
  position: absolute;
  color: var(--fa-primary-color, inherit);
  opacity: var(--fa-primary-opacity, 1); }

.fadt::after,
.fa-duotone.fa-thin::after {
  color: var(--fa-secondary-color, inherit);
  opacity: var(--fa-secondary-opacity, 0.4); }

.fa-swap-opacity .fadt::before,
.fa-swap-opacity .fa-duotone.fa-thin::before,
.fadt.fa-swap-opacity::before,
.fa-duotone.fa-swap-opacity::before,
.fa-duotone.fa-thin.fa-swap-opacity::before {
  opacity: var(--fa-secondary-opacity, 0.4); }

.fa-swap-opacity .fadt::after,
.fa-swap-opacity .fa-duotone.fa-thin::after,
.fadt.fa-swap-opacity::after,
.fa-duotone.fa-swap-opacity::after,
.fa-duotone.fa-thin.fa-swap-opacity::after {
  opacity: var(--fa-primary-opacity, 1); }

.fadt.fa-inverse,
.fa-duotone.fa-thin.fa-inverse {
  color: var(--fa-inverse, #fff); }

.fadt.fa-stack-1x,
.fadt.fa-stack-2x,
.fa-duotone.fa-thin.fa-stack-1x,
.fa-duotone.fa-thin.fa-stack-2x {
  position: absolute; }
