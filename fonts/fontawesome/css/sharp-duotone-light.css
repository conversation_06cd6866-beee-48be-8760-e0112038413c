/*!
 * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license (Commercial License)
 * Copyright 2024 Fonticons, Inc.
 */
:root, :host {
  --fa-style-family-sharp-duotone: 'Font Awesome 6 Sharp Duotone';
  --fa-font-sharp-duotone-light: normal 300 1em/1 'Font Awesome 6 Sharp Duotone'; }

@font-face {
  font-family: 'Font Awesome 6 Sharp Duotone';
  font-style: normal;
  font-weight: 300;
  font-display: block;
  src: url("../webfonts/fa-sharp-duotone-light-300.woff2") format("woff2"), url("../webfonts/fa-sharp-duotone-light-300.ttf") format("truetype"); }

.fasdl,
.fa-sharp-duotone.fa-light {
  position: relative;
  font-weight: 300;
  letter-spacing: normal; }

.fasdl::before,
.fa-sharp-duotone.fa-light::before {
  position: absolute;
  color: var(--fa-primary-color, inherit);
  opacity: var(--fa-primary-opacity, 1); }

.fasdl::after,
.fa-sharp-duotone.fa-light::after {
  color: var(--fa-secondary-color, inherit);
  opacity: var(--fa-secondary-opacity, 0.4); }

.fa-swap-opacity .fasdl::before,
.fa-swap-opacity .fa-sharp-duotone.fa-light::before,
.fasdl.fa-swap-opacity::before,
.fa-sharp-duotone.fa-swap-opacity::before,
.fa-sharp-duotone.fa-light.fa-swap-opacity::before {
  opacity: var(--fa-secondary-opacity, 0.4); }

.fa-swap-opacity .fasdl::after,
.fa-swap-opacity .fa-sharp-duotone.fa-light::after,
.fasdl.fa-swap-opacity::after,
.fa-sharp-duotone.fa-swap-opacity::after,
.fa-sharp-duotone.fa-light.fa-swap-opacity::after {
  opacity: var(--fa-primary-opacity, 1); }

.fasdl.fa-inverse,
.fa-sharp-duotone.fa-light.fa-inverse {
  color: var(--fa-inverse, #fff); }

.fasdl.fa-stack-1x,
.fasdl.fa-stack-2x,
.fa-sharp-duotone.fa-light.fa-stack-1x,
.fa-sharp-duotone.fa-light.fa-stack-2x {
  position: absolute; }
