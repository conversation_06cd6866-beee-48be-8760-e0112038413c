<?php 
$logo = get_field('logo', 'option');
?>

<!doctype html>
<html <?php language_attributes(); ?>>

<head>
	<meta charset="<?php bloginfo('charset'); ?>">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
	<header class="header">
		<div class="container">
			<div class="row flex align-center">
				<a class="header__logo" href="<?php echo esc_url(home_url('/')); ?>" rel="home">
					<img src="<?php echo esc_url($logo['url']); ?>" alt="<?php echo esc_attr($logo['alt']); ?>" />
				</a>
				<button class="menu__toggle"></button>
				<nav class="header__nav">
					<?php
					wp_nav_menu(
						array(
							'theme_location' => 'menu-1',
							'menu_class'     => 'header__menu',
							'depth'          => 2,
							'container'      => false,
							'fallback_cb'    => false,
							'items_wrap'     => '<ul class="%2$s">%3$s</ul>',
						)
					);
					?>
				</nav>
			</div>
		</div>
	</header>
	<div class="spacer"></div>