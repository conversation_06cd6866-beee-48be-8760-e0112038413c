<?php
/* Template Name: Homepage */

get_header();
?>

<?php
while (have_posts()) :
	the_post();

	// Flexible content
	if( have_rows('content_blocks') ):
		while (have_rows('content_blocks')) : the_row();
			$block_type = get_row_layout();
			switch ($block_type) {
				case 'hero':
					get_template_part('content-blocks/hero');
					break;
				case 'about':
					get_template_part('content-blocks/about');
					break;
				case 'benefits':
					get_template_part('content-blocks/benefits');
					break;
				case 'features':
					get_template_part('content-blocks/features');
					break;
				case 'steps':
					get_template_part('content-blocks/steps');
					break;
				case 'app':
					get_template_part('content-blocks/app');
					break;
				case 'services':
					get_template_part('content-blocks/services');
					break;
				case 'answers':
					get_template_part('content-blocks/answers');
					break;
				case 'meeting':
					get_template_part('content-blocks/meeting');
					break;
				case 'contact':
					get_template_part('content-blocks/contact');
					break;
				default:
					break;
			}
		endwhile;
	endif;

endwhile;

?>

<?php
get_footer();
