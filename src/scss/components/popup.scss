@use '../abstracts/variables' as variables;

body.popup-open {
    overflow: hidden;
}

.popup {
    opacity: 0;
    visibility: hidden;
    background-color: rgba(23, 22, 33, .9);
    align-items: center;
    display: flex;
    height: 100%;
    justify-content: center;
    left: 0;
    position: fixed;
    top: 0;
    transition: all .3s ease 0s;
    width: 100%;
    z-index: 9999;

    &.show {
        opacity: 1;
        visibility: visible;
    }
}

.admin-bar .popup {
    top: 32px;

    @media screen and (max-width: 782px) {
        top: 46px;
    }
}

.admin-bar.scrolled-under-admin-bar .popup {
    @media screen and (max-width: 600px) {
        top: 0;
    }
}

.popup__inner {
    background-color: #fff;
    border-radius: 8px;
    max-height: 100vh;
    overflow-x: hidden;
    overflow-y: auto;
    position: relative;
    max-width: calc(100% - 30px);
    width: 1000px;

    @media screen and (max-width: 1029px) {
        width: 100%;
        max-width: 100%;
    }

    @media screen and (max-width: 649px) {
        border-radius: 0;
    }
}

.popup__content {
    position: relative;
    height: 700px;
    max-height: 100vh;
    min-width: 320px;
    display: flex;
    justify-content: center;
    align-items: center;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 50px;
        background-color: #fff;

        @media screen and (max-width: 649px) {
            width: 40px;
            height: 40px;
            border-radius: 0;
        }
    }

    @media screen and (max-width: 1029px) {
        height: 1235px;
        width: 100%;
        max-width: 100%;
    }

}

.popup__close {
    appearance: none;
    background: none;
    border: none;
    cursor: pointer;
    position: absolute;
    right: 0;
    top: 0;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;

    @media screen and (max-width: 649px) {
        width: 40px;
        height: 40px;
    }

    &::before {
        content: '\f00d';
        font-family: "Font Awesome 6 Pro";
        -moz-osx-font-smoothing: grayscale;
        -webkit-font-smoothing: antialiased;
        display: inline-block;
        font-style: normal;
        font-variant: normal;
        line-height: 1;
        text-rendering: auto;
        font-size: 32px;
        transition: color 0.3s ease-in-out;

        @media screen and (max-width: 649px) {
            font-size: 26px;
        }
    }

    &:hover {
        color: variables.$light-blue;
    }
}