@use '../abstracts/variables' as variables;

.row--buttons {
    padding-top: 30px;
    gap: 8px;
}

.button {
    font-family: variables.$font-heading;
    font-size: 18px;
    line-height: 1;
    padding: 15px 25px;
    border-radius: 15px;
    transition: all 0.3s ease-in-out;
    text-decoration: none;
    border: 2px solid transparent;
}

.button--cta-white {
    background-color: variables.$inverted-text-color;
    border-color: variables.$inverted-text-color;
    color: variables.$light-blue;

    &:hover {
        color: variables.$text-color;
        box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.5);
        margin-top: -2px;
        margin-bottom: 2px;
    }
}

.button--link-white {
    background-color: transparent;
    color: variables.$inverted-text-color;

    &:hover {
        border-color: variables.$inverted-text-color;
    }
}

.button--cta {
    background-color: variables.$light-blue;
    border-color: variables.$light-blue;
    color: variables.$inverted-text-color;

    &:hover {
        background-color: variables.$dark-blue;
        border-color: variables.$dark-blue;
    }
}

.button--link {
    background-color: transparent;
    color: variables.$text-color;

    &:hover {
        border-color: variables.$text-color;
    }
}

.button--cta-alt {
    background-color: variables.$light-blue;
    border-color: variables.$light-blue;
    color: variables.$inverted-text-color;

    &:hover {
        background-color: variables.$inverted-text-color;
        border-color: variables.$inverted-text-color;
        color: variables.$light-blue;
        margin-top: -2px;
        margin-bottom: 2px;
    }
}