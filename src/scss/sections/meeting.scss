@use '../abstracts/variables' as variables;

.meeting {
    background: linear-gradient(180deg, variables.$dark-blue 0%, variables.$light-blue 55.73%, variables.$dark-blue 100%);
    color: variables.$inverted-text-color;
    padding-top: 7%;
    padding-bottom: 70px;

    .row--buttons {
        padding-top: 60px;
    }
}

.meeting__row {
    gap: 10px;

    @media screen and (max-width: variables.$breakpoint-sm-max) {
        flex-direction: column;
        align-items: center;
    }
}

.meeting__content {
    width: 380px;
    max-width: 100%;

    h2 {
        margin-bottom: 50px;
    }

}

.meeting__buttons {
    margin-top: 20px;
    margin-bottom: 10px;
}