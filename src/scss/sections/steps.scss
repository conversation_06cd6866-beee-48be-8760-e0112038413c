@use '../abstracts/variables' as variables;

.steps {
    background-color: variables.$grey-dark;
    padding-top: 100px;
    padding-bottom: 100px;

    .row--buttons {
        padding-top: 60px;
    }
}

.steps__text {
    p {
        font-family: variables.$font-heading;
        font-style: normal;
        font-weight: 400;
        font-size: 30px;
        line-height: 36px;
    }
}

.step__items {
    width: 1040px;
    max-width: 100%;
    margin: 50px auto;
    flex-direction: column;
    gap: 20px;
}

.step {
    background-color: variables.$inverted-text-color;
    border-radius: 10px;
    padding: 30px 70px;
    gap: 50px;
    align-items: center;

    @media screen and (max-width: variables.$breakpoint-sm-max) {
        flex-direction: column;
        padding: 30px 40px;
        gap: 20px;
    }
}

.step__icon {
    flex-shrink: 0;
    width: 90px;
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: center;

    i {
        font-size: 64px;
        color: variables.$light-blue;
    }
}

.step__text {
    p {
        font-family: variables.$font-heading;
        font-style: normal;
        font-weight: 400;
        font-size: 20px;
        line-height: 24px;
        text-align: left;

        @media screen and (max-width: variables.$breakpoint-sm-max) {
            text-align: center;
        }

        &:last-child {
            margin-bottom: 0;
        }
    }
}