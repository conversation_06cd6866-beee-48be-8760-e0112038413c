@use '../abstracts/variables' as variables;

.services {
    padding-top: 80px;
    padding-bottom: 60px;

    .row--buttons {
        padding-top: 60px;
    }
}

.services__text {
    h2 {
        margin-bottom: 40px;
    }
}

.services__items {
    margin-top: 40px;
    gap: 20px;
    justify-content: center;

    .col {
        background-color: variables.$grey;
        border-radius: 10px;
        padding: 30px 20px;
        flex-basis: calc(100% / 4 - 60px / 4);
        max-width: calc(100% / 4 - 60px / 4);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;

        @media screen and (max-width: variables.$breakpoint-md-max) {
            flex-basis: calc(100% / 2 - 20px / 2);
            max-width: calc(100% / 2 - 20px / 2);
        }

        @media screen and (max-width: variables.$breakpoint-sm-max) {
            flex-basis: 100%;
            max-width: 100%;
        }

        i {
            // width: auto;
            // height: 67px;
            color: variables.$light-blue;
            font-size: 48px;
        }

        h3 {
            width: 250px;
            max-width: 100%;
            font-size: 18px;
            line-height: 140%;
        }

        p:last-child {
            margin-bottom: 0;
        }
    }
}

.service__icon {
    height: 67px;
    display: flex;
    align-items: center;
    justify-content: center;
}