@use '../abstracts/variables' as variables;

.hero {
    padding: 67px 0 100px;
    background: linear-gradient(180deg, variables.$dark-blue 23.5%, variables.$darker-blue 44.5%, variables.$mid-blue 55%, variables.$light-blue 72%, #213685 86%, variables.$dark-blue 100%);
    color: variables.$inverted-text-color;

    h1 {
        margin-bottom: 43px;
    }
}

.hero__content {
    @media screen and (max-width: variables.$breakpoint-md-max) {
        br {
            display: none;
        }
    }
}

.hero__items {
    margin-top: 30px;
    margin-bottom: 15px;
    gap: 20px;

    @media screen and (max-width: variables.$breakpoint-md-max) {
        justify-content: center;
    }

    .col {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
}

.hero__items__img {
    flex: 0 0 calc(440 / 1400 * 100%);
    max-width: calc(440 / 1400 * 100% + 20px);
    align-self: center;

    @media screen and (min-width: variables.$breakpoint-md-min) {
        margin-right: -20px;
    }

    @media screen and (max-width: variables.$breakpoint-md-max) {
        flex: 0 0 100%;
        max-width: 100%;
    }

    img {
        width: 580px;
    }
}

.hero__items__icon {
    flex: 0 0 calc(980 / 1400 * 100% / 3 - 20px);
    max-width: calc(980 / 1400 * 100% / 3 - 20px);

    @media screen and (min-width: variables.$breakpoint-md-min) {
        padding-top: 14.5px;
    }

    @media screen and (max-width: variables.$breakpoint-md-max) {
        flex: 0 0 320px;
        max-width: calc(100% / 3 - 40px / 3);
    }

    @media screen and (max-width: variables.$breakpoint-sm-max) {
        flex: 0 0 100%;
        max-width: 100%;
    }

    img, svg {
        width: auto;
        height: 117px;
        display: block;
        margin: 0 auto;

    }
    
    &:nth-child(3) {
        img, svg {
            margin-top: 6.5px;
            margin-bottom: 6.5px;
            height: 104px;
        }
    }

    h2 {
        margin-top: 13px;
        margin-bottom: 13px;
    }

    p {
        width: 250px;
        max-width: 100%;
    }
}
