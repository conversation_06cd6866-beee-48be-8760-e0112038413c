@use '../abstracts/variables' as variables;

.benefits {
    padding-top: 80px;
    padding-bottom: 100px;
    background-color: variables.$grey;

    .row--buttons {
        padding-top: 60px;
    }
}

.benefits__items {
    margin-top: 65px;
    gap: 20px;
    justify-content: center;

    .col {
        flex-basis: calc(100% / 5 - 80px / 5);
        max-width: calc(100% / 5 - 80px / 5);
        display: flex;
        flex-direction: column;
        align-items: center;

        @media screen and (max-width: variables.$breakpoint-md-max) {
            flex-basis: 240px;
            max-width: 100%;
        }

        i {
            padding: 5px 40px;
            font-size: 64px;
            color: variables.$light-blue;
        }

        h3 {
            margin-top: 15px;
            margin-bottom: 15px;
        }
    }
}

.benefits__text {
    width: 1040px;
    max-width: 100%;
    margin: 65px auto 0;
    
    p {
        text-align: left;
    }
}

.benefits__accordion {
    margin-top: 30px;
}
