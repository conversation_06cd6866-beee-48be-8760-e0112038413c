@use '../abstracts/variables' as variables;

.contact {
    background-color: variables.$grey-light;
    padding-top: 80px;
    padding-bottom: 90px;
}

.contact__content {
    h2 {
        margin-bottom: 50px;
    }
}

.contact__form {
    position: relative;
    width: 1040px;
    max-width: 100%;
    margin: 45px auto 0;

    @media screen and (max-width: variables.$breakpoint-xs-max) {
        margin-bottom: -52px;
    }

    .row {
        padding: 0;
        gap: 32px;

        .col {
            flex-basis: calc(100% / 2 - 32px / 2);
            max-width: calc(100% / 2 - 32px / 2);

            @media screen and (max-width: variables.$breakpoint-sm-max) {
                flex-basis: 100%;
                max-width: 100%;
            }
        }   

    }

    label {
        display: block;
        line-height: 180%;
        margin-bottom: 6px;
    }
    
    .form-field {
        input, textarea, select, button {
            display: block;
            width: 100%;
            max-width: 100%;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(#000, 0.5);
            font-family: variables.$font-heading;
            font-weight: 325;

            &:focus {
                outline: none;
            }
        }
    }

    textarea {
        resize: vertical;
        height: 100%;
    }

    input[type="submit"] {
        width: auto;
        background-color: variables.$light-blue;
        border-color: variables.$light-blue;
        color: variables.$inverted-text-color;
        cursor: pointer;
        font-family: variables.$font-heading;
        font-size: 18px;
        line-height: 1;
        padding: 15px 25px;
        border-radius: 15px;
        transition: all 0.3s ease-in-out;
        text-decoration: none;
        border: 2px solid transparent;
        
        &:hover {
            background-color: variables.$dark-blue;
            border-color: variables.$dark-blue;
        }
    }

    .form-field {
        margin-bottom: 10px;

        &:last-child {
            margin-bottom: 0;
        }
    }

}

.contact-form__row {
    gap: 24px;

    .form-field {
        flex-basis: calc(100% / 2 - 24px / 2);
        max-width: calc(100% / 2 - 24px / 2);
    }
}

.hf-message-success {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: variables.$grey-light;
    margin-bottom: 0;
    padding: 0 30px;

    i {
        font-size: 64px;
        color: variables.$green;
    }

    span {
        display: block;
        margin-top: 32px;
        font-family: variables.$font-heading;
        font-size: 20px;
        line-height: 140%;
        margin-bottom: 0;
    }
}

.hf-message-warning {
    margin-top: 25px;
    font-family: variables.$font-heading;
    display: flex;
    justify-content: center;
    align-items: center;
    color: variables.$red;

    &::before {
        content: '\e417';
        font-family: "Font Awesome 6 Pro";
        font-weight: 400;
        margin-right: 12px;
        font-size: 24px;
        -moz-osx-font-smoothing: grayscale;
        -webkit-font-smoothing: antialiased;
        display: inline-block;
        font-style: normal;
        font-variant: normal;
        text-rendering: auto;
    }
}

.contact__form .contact__submit {
    margin-top: 24px;
    gap: 32px;

    @media screen and (max-width: variables.$breakpoint-xs-max) {
        flex-direction: column;
        gap: 24px;
    }

    .col:last-child {
        text-align: right;

        @media screen and (max-width: variables.$breakpoint-xs-max) {
            text-align: left;
        }
    }
}