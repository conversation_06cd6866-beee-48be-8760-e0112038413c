<?php
// App section for flexible content block
$anchor     = get_sub_field('anchor');
$heading    = get_sub_field('heading');
$features   = get_sub_field('features');
$text_box   = get_sub_field('text_box');
$carousel   = get_sub_field('carousel');
$buttons    = get_sub_field('buttons');
?>
<section class="app" <?php if ($anchor) : ?>id="<?php echo esc_attr($anchor); ?>"<?php endif; ?>>
    <div class="container">
        <div class="row flex app__row">
            <div class="col app__content">
                <div class="row">
                    <?php if ($heading) : ?>
                        <h2><?php echo esc_html($heading); ?></h2>
                    <?php endif; ?>
                </div>
                <?php if ($features) : ?>
                    <div class="row flex flex-wrap justify-center app__features">
                        <?php foreach ($features as $feature) : ?>
                            <div class="col">
                                <?php if ($feature['icon']) : ?>
                                    <div class="app__icon">
                                        <?php echo $feature['icon']; ?>
                                    </div>
                                <?php endif; ?>
                                <?php if ($feature['text']) : ?>
                                    <div class="app__feature"><?php echo wp_kses_post($feature['text']); ?></div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                <?php if ($text_box) : ?>
                    <div class="row app__text">
                        <?php echo wp_kses_post($text_box); ?>
                    </div>
                <?php endif; ?>
                <?php if ($buttons) : ?>
                    <div class="row flex flex-wrap justify-center app__buttons">
                        <?php foreach ($buttons as $button) : ?>
                            <a href="<?php echo esc_url($button['link']['url']); ?>" target="<?php echo esc_attr($button['link']['target']); ?>">
                                <img src="<?php echo esc_url($button['img']['url']); ?>" alt="<?php echo esc_attr($button['img']['alt']); ?>" />
                            </a>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
            <div class="col app__slider">
                <?php if ($carousel) : ?>
                    <div class="swiper-container">
                        <div class="swiper-wrapper">
                            <?php foreach ($carousel as $image) : ?>
                                <div class="swiper-slide">
                                    <img src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>