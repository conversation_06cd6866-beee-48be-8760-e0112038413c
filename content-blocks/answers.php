<?php
// Q&A section for flexible content block
$anchor     = get_sub_field('anchor');
$heading    = get_sub_field('heading');
$answers    = get_sub_field('answers');
$button_1   = get_sub_field('button_1');
$button_2   = get_sub_field('button_2');
?>
<section class="answers" <?php if ($anchor) : ?>id="<?php echo esc_attr($anchor); ?>"<?php endif; ?>>
    <div class="container">
        <div class="row answers__text">
            <?php if ($heading) : ?>
                <h2><?php echo esc_html($heading); ?></h2>
            <?php endif; ?>
        </div>
        <?php if ($answers) : ?>
            <div class="row accordion answers__accordion">
                <?php 
                $i = 0;
                foreach ($answers as $item) : 
                    $active = $i === 0 ? 'active' : '';
                    $i++;
                    ?>
                    <div class="accordion-item <?php echo $active; ?>">
                        <div class="accordion-header">
                            <?php if ($item['title']) : ?>
                                <h3><span><?php echo esc_html($item['title']); ?></span></h3>
                            <?php endif; ?>
                        </div>
                        <div class="accordion-content">
                            <?php if ($item['text']) : ?>
                                <?php echo wp_kses_post($item['text']); ?>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        <div class="row flex justify-center">
            <?php if ($button_1) : ?>
                <a href="<?php echo esc_url($button_1['url']); ?>" target="<?php echo esc_attr($button_1['target']); ?>">
                    <?php echo esc_html($button_1['title']); ?>
                </a>
            <?php endif; ?>
            <?php if ($button_2) : ?>
                <a href="<?php echo esc_url($button_2['url']); ?>" target="<?php echo esc_attr($button_2['target']); ?>">
                    <?php echo esc_html($button_2['title']); ?>
                </a>
            <?php endif; ?>
        </div>
    </div>
</section>