<?php
// About section for flexible content block
$anchor     = get_sub_field('anchor');
$img        = get_sub_field('img');
$img_mobile = get_sub_field('img_mobile');
$up_text    = get_sub_field('up_text');
$tags       = get_sub_field('tags');
$heading    = get_sub_field('heading');
$text       = get_sub_field('text');
$solutions  = get_sub_field('solutions');
$button_1   = get_sub_field('button_1');
$button_2   = get_sub_field('button_2');
?>
<section class="about" <?php if ($anchor) : ?>id="<?php echo esc_attr($anchor); ?>"<?php endif; ?>>
    <div class="container">
        <div class="row flex justify-center">
            <div class="content-desktop">
                <?php if ($img) : ?>
                    <?php echo wp_kses_post($img); ?>
                <?php endif; ?>
            </div>
            <div class="content-mobile">
                <?php if ($img_mobile) : ?>
                    <?php echo wp_kses_post($img_mobile); ?>
                <?php endif; ?>
            </div>
        </div>
        <div class="row about__text">
            <?php if ($up_text) : ?>
                <?php echo wp_kses_post($up_text); ?>
            <?php endif; ?>
        </div>
        <?php if ($tags) : ?>
            <div class="row flex justify-center flex-wrap about__tags">
                <?php foreach ($tags as $tag) :
                    if ($tag['text']) : ?>
                        <div class="tag">
                            <?php echo wp_kses_post($tag['text']); ?>
                        </div>
                    <?php endif;
                endforeach; ?>
            </div>
        <?php endif; ?>
        <div class="row about__content">
            <?php if ($heading) : ?>
                <h2><?php echo esc_html($heading); ?></h2>
            <?php endif; ?>
            <?php if ($text) : ?>
                <?php echo wp_kses_post($text); ?>
            <?php endif; ?>
        </div>
        <?php if ($solutions) : ?>
            <div class="row flex flex-wrap about__items">
                <?php foreach ($solutions as $solution) : ?>
                    <div class="col">
                        <?php if ($solution['heading']) : ?>
                            <h3><?php echo esc_html($solution['heading']); ?></h3>
                        <?php endif; ?>
                        <?php if ($solution['text']) : ?>
                            <?php echo wp_kses_post($solution['text']); ?>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        <div class="row flex justify-center flex-wrap row--buttons">
            <?php if ($button_1) : ?>
                <a class="button button--cta" href="<?php echo esc_url($button_1['url']); ?>" target="<?php echo esc_attr($button_1['target']); ?>">
                    <?php echo esc_html($button_1['title']); ?>
                </a>
            <?php endif; ?>
            <?php if ($button_2) : ?>
                <a class="button button--link" href="<?php echo esc_url($button_2['url']); ?>" target="<?php echo esc_attr($button_2['target']); ?>">
                    <?php echo esc_html($button_2['title']); ?>
                </a>
            <?php endif; ?>
        </div>
    </div>
</section>