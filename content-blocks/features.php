<?php
// Features section for flexible content block
$anchor     = get_sub_field('anchor');
$heading    = get_sub_field('heading');
$functions  = get_sub_field('functions');
$img        = get_sub_field('img');
$img_mobile = get_sub_field('img_mobile');
$button_1   = get_sub_field('button_1');
$button_2   = get_sub_field('button_2');
?>
<section class="features" <?php if ($anchor) : ?>id="<?php echo esc_attr($anchor); ?>"<?php endif; ?>>
    <div class="container">
        <div class="row">
            <?php if ($heading) : ?>
                <h2><?php echo esc_html($heading); ?></h2>
            <?php endif; ?>
        </div>
        <?php if ($functions) : ?>
            <div class="row flex flex-wrap features__items">
                <?php foreach ($functions as $function) : ?>
                    <div class="col flex text-left feature">
                        <?php if ($function['icon']) : ?>
                            <div class="feature__icon">
                                <?php echo $function['icon']; ?>
                            </div>
                        <?php endif; ?>
                        <?php if ($function['text']) : ?>
                            <div class="feature__text">
                                <?php echo wp_kses_post($function['text']); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        <div class="row flex justify-center features__img">
            <div class="content-desktop">
                <?php if ($img) : ?>
                    <?php echo wp_kses_post($img); ?>
                <?php endif; ?>
            </div>
            <div class="content-mobile">
                <?php if ($img_mobile) : ?>
                    <?php echo wp_kses_post($img_mobile); ?>
                <?php endif; ?>
            </div>
        </div>
        <div class="row flex justify-center flex-wrap row--buttons">
            <?php if ($button_1) : ?>
                <a class="button button--cta" href="<?php echo esc_url($button_1['url']); ?>" target="<?php echo esc_attr($button_1['target']); ?>">
                    <?php echo esc_html($button_1['title']); ?>
                </a>
            <?php endif; ?>
            <?php if ($button_2) : ?>
                <a class="button button--link" href="<?php echo esc_url($button_2['url']); ?>" target="<?php echo esc_attr($button_2['target']); ?>">
                    <?php echo esc_html($button_2['title']); ?>
                </a>
            <?php endif; ?>
        </div>
    </div>
</section>